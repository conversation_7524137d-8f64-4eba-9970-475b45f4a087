{"name": "isotope-ai-time-management", "main": "expo-router/entry", "version": "1.0.0", "private": true, "scripts": {"dev": "EXPO_NO_TELEMETRY=1 expo start", "build:web": "expo export --platform web", "lint": "expo lint"}, "dependencies": {"@expo/vector-icons": "^14.2.0", "@expo-google-fonts/inter": "^0.2.3", "expo": "^53.0.0", "expo-blur": "~15.0.0", "expo-camera": "~16.2.0", "expo-constants": "~17.2.0", "expo-font": "~13.3.0", "expo-haptics": "~14.2.0", "expo-linear-gradient": "~14.2.0", "expo-linking": "~7.2.0", "expo-router": "~4.1.0", "expo-splash-screen": "~0.31.0", "expo-status-bar": "~2.3.0", "expo-symbols": "~0.5.0", "expo-system-ui": "~5.1.0", "expo-web-browser": "~14.2.0", "lucide-react-native": "^0.475.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.80.0", "react-native-gesture-handler": "~2.25.0", "react-native-reanimated": "~3.18.0", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.0", "react-native-svg": "15.12.0", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0", "react-native-webview": "13.14.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}}